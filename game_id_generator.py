#!/usr/bin/env python3

import random
import string

def generate_game_id():
    """Generate a random 8-character alphanumeric game ID"""
    characters = string.ascii_uppercase + string.digits
    return ''.join(random.choice(characters) for _ in range(8))

def generate_game_type(numbers):
    """Generate game_type based on number of numbers picked"""
    if isinstance(numbers, list):
        count = len(numbers)
    else:
        count = numbers
    return f"turbo-{count}"

def create_sample_ticket(numbers, position="last", stake_amount=100):
    """Create a sample ticket with proper game_id and game_type"""
    return {
        "game_id": generate_game_id(),
        "game_type": generate_game_type(numbers),
        "stake_amount": stake_amount,
        "numbers": numbers,
        "position": position
    }

if __name__ == "__main__":
    print("Game ID Generator for NAP2 Turbo")
    print("=" * 40)
    
    # Generate some sample game IDs
    print("Sample Game IDs:")
    for i in range(5):
        print(f"  {generate_game_id()}")
    
    print("\nSample Tickets:")
    
    # Sample tickets
    tickets = [
        create_sample_ticket([1, 3, 2], "last", 100),
        create_sample_ticket([88, 89], "first", 200),
        create_sample_ticket([1, 2, 3, 4], "last", 150),
        create_sample_ticket([5, 10, 15, 20, 25], "first", 50)
    ]
    
    for ticket in tickets:
        print(f"  Game ID: {ticket['game_id']}")
        print(f"  Game Type: {ticket['game_type']}")
        print(f"  Numbers: {ticket['numbers']} ({len(ticket['numbers'])} numbers)")
        print(f"  Position: {ticket['position']}")
        print(f"  Stake: {ticket['stake_amount']}")
        print()
    
    # Generate a complete request payload
    print("Sample Request Payload:")
    print("-" * 30)
    import json
    
    payload = {
        "tickets": tickets,
        "pool_value": 400000
    }
    
    print(json.dumps(payload, indent=2))
