from itertools import combinations
from collections import Counter
from pprint import pprint
import random
from datetime import datetime

def generate_random_ticket(size, min_num=1, max_num=83):
    """Generates a random ticket with unique numbers."""
    return set(random.sample(range(min_num, max_num + 1), size))

def generate_random_ticket2(size, min_num=1, max_num=50):
    """Generates a random ticket with unique numbers."""
    return set(random.sample(range(min_num, max_num + 1), size))

def simulate_tickets(num_tickets=1000, stake=10):
    random_tickets = []

    for _ in range(num_tickets):
        # Generate tickets for NAP, PERM, and AGAINST

        # NAP Ticket: 5 chosen numbers
        nap_chosen_numbers = generate_random_ticket(random.choice(range(2, 5)))

        # PERM Tickets: perm1 to perm4 (5 chosen numbers, 3 to 20 pool numbers)
        perm_tickets = generate_random_ticket(random.choice(range(2, 11)))

        # AGAINST Ticket: 7 numbers for top and bottom, 3 to 20 pool numbers
        against_chosen_top = generate_random_ticket(5)
        against_chosen_bottom = generate_random_ticket2(7)

        random_tickets.append({'nap': (nap_chosen_numbers, datetime.now().timestamp(), random.choice(range(100,1000)), random.choice(range(100,1000))) }),
        random_tickets.append({f'perm-{random.choice(range(2, 5))}': (perm_tickets, datetime.now().timestamp(),random.choice(range(5,100)), random.choice(range(100,1000)))}),
        random_tickets.append({'against': ((against_chosen_top, against_chosen_bottom),datetime.now().timestamp(), random.choice(range(5,100)), random.choice(range(100,1000)))}),
        random_tickets.append({'banker': ({random.choice(range(30,70)),}, datetime.now().timestamp(), random.choice(range(100,1000)), random.choice(range(100,1000)))}),

    return random_tickets


def count_tickets(tickets, max_range=91, previous_winning_number=[]):
    all_numbers = list(range(1,max_range))
    all_numbers.extend(previous_winning_number*20000)

    for ticket in tickets:
        for ticket_type, numbers in ticket.items():
            all_numbers.extend(set(numbers[0]) if ticket_type != 'against' else set(numbers[0][0]) | set(numbers[0][1]))

    all_numbers = [number for number in all_numbers if number < max_range]
    # pprint(all_numbers)
    number_counts = Counter(all_numbers)
    pprint(number_counts)

    CHOICE_OF_MAX = random.choice([5,6,7,8,9,10,11,12,13,14,15,8,9,10,11,12,13,14,15,13,14,15,8,9,10,11,12,13,14,15])
    # print("CHOICE_OF_MAX :::::::::::::::::: ", CHOICE_OF_MAX)

    most_common = number_counts.most_common()[:CHOICE_OF_MAX] if not previous_winning_number else []
    least_common = number_counts.most_common()[-10:]
    mode_common = number_counts.most_common()[35:50] if not previous_winning_number else []

    # print("Most common number:", most_common)
    # print("Least common number:", least_common)

    elements, counts = zip(*(mode_common+most_common+least_common))

    return elements, CHOICE_OF_MAX


def generate_combinations(elements, r):
    return list(combinations(elements, r))

if __name__ == "__main__":

    tickets = simulate_tickets()

    count_tickets(tickets)

