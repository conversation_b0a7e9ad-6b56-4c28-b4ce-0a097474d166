#!/usr/bin/env python3

import json
import random

def generate_game_id(ticket_num):
    """Generate game ID in format T0000XXX"""
    return f"T{ticket_num:07d}"

def generate_ticket_data():
    """Generate comprehensive test dataset with 200 tickets"""
    
    tickets = []
    random.seed(42)  # For reproducible results
    
    # Ticket generation patterns
    stake_amounts = [50, 75, 100, 120, 150, 180, 200, 250, 300, 350, 400, 450, 500]
    positions = ["first", "last"]
    
    for i in range(1, 201):  # Generate tickets T0000001 to T0000200
        # Determine turbo type (2-5 numbers)
        turbo_type = ((i - 1) % 4) + 2  # Cycles through 2, 3, 4, 5
        
        # Generate numbers based on different patterns
        if i <= 50:
            # Pattern 1: Sequential numbers
            start_num = (i - 1) * 2 + 1
            numbers = list(range(start_num, start_num + turbo_type))
        elif i <= 100:
            # Pattern 2: Multiples
            base = (i - 50)
            numbers = [base * j for j in range(1, turbo_type + 1)]
        elif i <= 150:
            # Pattern 3: Random numbers
            numbers = sorted(random.sample(range(1, 91), turbo_type))
        else:
            # Pattern 4: High numbers (60-90 range)
            numbers = sorted(random.sample(range(60, 91), turbo_type))
        
        # Ensure numbers are within valid range (1-90)
        numbers = [min(90, max(1, num)) for num in numbers]
        numbers = sorted(list(set(numbers)))  # Remove duplicates and sort
        
        # If we lost numbers due to duplicates/range limits, fill in
        while len(numbers) < turbo_type:
            candidate = random.randint(1, 90)
            if candidate not in numbers:
                numbers.append(candidate)
        
        numbers = sorted(numbers[:turbo_type])  # Ensure exact count
        
        ticket = {
            "game_id": generate_game_id(i),
            "game_type": f"turbo-{turbo_type}",
            "stake_amount": random.choice(stake_amounts),
            "numbers": numbers,
            "position": positions[i % 2]  # Alternate between first and last
        }
        
        tickets.append(ticket)
    
    # Create the complete dataset
    dataset = {
        "tickets": tickets,
        "pool_value": 2000000  # Large pool value for 200 tickets
    }
    
    return dataset

def main():
    print("Generating 200-ticket test dataset...")
    
    dataset = generate_ticket_data()
    
    # Save to JSON file
    with open("test_dataset_200_tickets.json", "w") as f:
        json.dump(dataset, f, indent=2)
    
    print(f"Generated {len(dataset['tickets'])} tickets")
    print(f"Pool value: {dataset['pool_value']:,}")
    
    # Print some statistics
    turbo_counts = {}
    position_counts = {"first": 0, "last": 0}
    stake_range = {"min": float('inf'), "max": 0}
    
    for ticket in dataset['tickets']:
        turbo_type = ticket['game_type']
        turbo_counts[turbo_type] = turbo_counts.get(turbo_type, 0) + 1
        
        position_counts[ticket['position']] += 1
        
        stake = ticket['stake_amount']
        stake_range['min'] = min(stake_range['min'], stake)
        stake_range['max'] = max(stake_range['max'], stake)
    
    print("\nDataset Statistics:")
    print("Turbo type distribution:")
    for turbo_type, count in sorted(turbo_counts.items()):
        print(f"  {turbo_type}: {count} tickets")
    
    print(f"\nPosition distribution:")
    print(f"  First: {position_counts['first']} tickets")
    print(f"  Last: {position_counts['last']} tickets")
    
    print(f"\nStake amount range: {stake_range['min']} - {stake_range['max']}")
    
    print("\nFirst 5 tickets:")
    for i in range(5):
        ticket = dataset['tickets'][i]
        print(f"  {ticket['game_id']}: {ticket['game_type']}, stake {ticket['stake_amount']}, numbers {ticket['numbers']}, position {ticket['position']}")
    
    print("\nLast 5 tickets:")
    for i in range(-5, 0):
        ticket = dataset['tickets'][i]
        print(f"  {ticket['game_id']}: {ticket['game_type']}, stake {ticket['stake_amount']}, numbers {ticket['numbers']}, position {ticket['position']}")

if __name__ == "__main__":
    main()
