from gen_tickets import simulate_tickets, count_tickets, generate_combinations
from multiprocessing import Process, Queue
from itertools import combinations
from pprint import pprint

def perm_find_matching_pairs(chosen_numbers, pool_numbers, stake=100, perm_type=2):
    """
    Finds pairs of numbers from the chosen numbers that appear in the pool numbers.

    :param chosen_numbers: A set of 5 numbers.
    :param pool_numbers: A set of 3 to 20 numbers.
    :return: A list of matching pairs.
    """
    # print(perm_type)
    if perm_type == 2:
        perm_multiplers = {
                    0:0,
                    1:0,
                    2:1,
                    3: 3,
                    4: 6,
                    5: 10,
                    6: 15,
                    7: 21,
                    8: 28,
                    9: 36,
                    10: 45,
                    11: 55,
                    12: 66,
                    13: 78,
                    14: 91,
                    15: 105,
                    16: 120,
                    17: 136,
                    18: 153,
                    19: 171,
                    20: 190
                    }
        win_multiplier = 240

    elif perm_type == 3:
        perm_multiplers = {
                    0:0,
                    1:0,
                    2:0,
                    3:1,
                    4: 4,
                    5: 10,
                    6: 20,
                    7: 35,
                    8: 56,
                    9: 84,
                    10: 120,
                    11: 165,
                    12: 220,
                    13: 286,
                    14: 364,
                    15: 455,
                    16: 560,
                    17: 680,
                    18: 816,
                    19: 969,
                    20: 1140
                    }
        win_multiplier = 2100

    elif perm_type == 4:
        perm_multiplers = {
                    0:0,
                    1:0,
                    2:0,
                    3:0,
                    4:1,
                    5: 5,
                    6: 15,
                    7: 35,
                    8: 70,
                    9: 126,
                    10: 210,
                    11: 330,
                    12: 495,
                    13: 715,
                    14: 1001,
                    15: 1365,
                    16: 1820,
                    17: 2380,
                    18: 3060,
                    19: 3876,
                    20: 4845
                    }
        win_multiplier = 6000
    elif perm_type == 5:
        perm_multiplers = {
                    0:0,
                    1:0,
                    2:0,
                    3:0,
                    4:0,
                    5: 1,
                    6: 6,
                    7: 21,
                    8: 56,
                    9: 126,
                    10: 252,
                    11: 462,
                    12: 792,
                    13: 1287,
                    14: 2002,
                    15: 3003,
                    16: 4368,
                    17: 6188,
                    18: 8568,
                    19: 11628,
                    20: 15504
                    }
        win_multiplier = 44000


    chosen_set = set(chosen_numbers)
    pool_set = set(pool_numbers)

    # Find the intersection between the two sets
    matching_numbers = chosen_set.intersection(pool_set)

    # Generate all possible pairs from the matching numbers
    matches = list(combinations(matching_numbers, 2))
    # print(matching_numbers)
    total_matches = len(matches)


    if len(matching_numbers) >= perm_type:
        multiplier_key = len(chosen_numbers)
    else:
        multiplier_key = 0
        win_multiplier = 0

    # print("HERE",len(matching_numbers), perm_type, matches)

    return (perm_multiplers[multiplier_key], matches), stake * len(matches) * win_multiplier * 1


def nap_find_matching_pairs(chosen_numbers, pool_numbers, stake=100):

    chosen_set = set(chosen_numbers)
    pool_set = set(pool_numbers)

    nap_multiplers = {
                    0:0,
                    1:290000,
                    2:240,
                    3:2100,
                    4:6000,
                    5:44000,
                    }

    has_match = chosen_set.issubset(pool_set)

    if has_match:
        # Find the intersection between the two sets
        matching_numbers = 1 # len(chosen_set)
    else:
        matching_numbers = 0

    # Generate all possible pairs from the matching numbers
    return matching_numbers, matching_numbers * stake * nap_multiplers.get(len(chosen_numbers))


def against_find_matching_pairs(chosen_top, chosen_bottom, pool_numbers, stake=100, WIN_MULTIPLIER=240):

    chosen_set_top = set(chosen_top)
    chosen_set_bottom = set(chosen_bottom)
    pool_set = set(pool_numbers)
    total_numbers_picked = len(chosen_bottom) + len(chosen_top)
    unit_stake = stake / total_numbers_picked

    top_intersect = chosen_set_top.intersection(pool_set)
    bottom_intersect = chosen_set_bottom.intersection(pool_set)

    # print(top_intersect)
    # print(bottom_intersect)

    top_matches = len(top_intersect)
    bottom_matches = len(bottom_intersect)

    multiplier = top_matches * bottom_matches

    # print("THIS:::", WIN_MULTIPLIER)
    # # Generate all possible pairs from the matching numbers
    return multiplier, multiplier * unit_stake * WIN_MULTIPLIER


def banker_find_matching_pairs(chosen_numbers, pool_numbers, stake=100):

    matches = len(set(chosen_numbers).intersection(set(pool_numbers)))

    if matches:
        matching_numbers = 1
        single_stake = stake/89
    else:
        matching_numbers = 0
        single_stake = 0

    return matching_numbers, single_stake * 960


def chunk_list(lst, n):
    if n <= 0:
        raise ValueError("n must be greater than 0")

    chunk_size = len(lst) // n
    remainder = len(lst) % n

    chunks = []
    start = 0

    for i in range(n):
        end = start + chunk_size + (1 if i < remainder else 0)
        chunks.append(lst[start:end])
        start = end

    return chunks



# pool_numbers = {31, 16, 11, 8, 33}  # Pool of drawn numbers

def process_tickets(tickets, pool_numbers, WIN_MULTIPLIER, return_all=False, lottery_type=None):
    total_winnings = 0
    total_winners = 0
    winning_numbers = []

    # Apply k30 multiplier if needed
    multiplier = 0.5 if lottery_type == "k_30" else 1

    for ticket in tickets:
        for ticket_type, numbers in ticket.items():
            if ticket_type == 'nap':
                matches, winnings = nap_find_matching_pairs(numbers[0], pool_numbers, numbers[-1])

            elif ticket_type.startswith('perm'):
                perm_type = int(ticket_type.split('-')[-1])
                matches, winnings = perm_find_matching_pairs(numbers[0], pool_numbers, perm_type=perm_type, stake=numbers[-2])

            elif ticket_type == 'against':
                chosen_top, chosen_bottom = numbers[0]
                matches, winnings = against_find_matching_pairs(chosen_top, chosen_bottom, pool_numbers, stake=numbers[-1], WIN_MULTIPLIER=WIN_MULTIPLIER)

            elif ticket_type == 'banker':
                matches, winnings = banker_find_matching_pairs(numbers[0], pool_numbers, stake=numbers[-1])

            else:
                continue

            # Apply k30 multiplier to winnings
            winnings *= multiplier

            if winnings:
                total_winners += 1
                if return_all:
                    winning_numbers.append((matches, ticket, winnings,))

            total_winnings += winnings

    return winning_numbers, total_winners, total_winnings


def draw(least_combinations, tickets, WIN_MULTIPLIER, queue=None, lottery_type=None):
    play_dict = {}
    for pool_numbers in least_combinations:
        _, total_winners, total_winnings = process_tickets(tickets, pool_numbers, WIN_MULTIPLIER, lottery_type=lottery_type)
        play_dict[pool_numbers] = total_winnings, total_winners
    print("DRAW COMPLETE.!!")

    if queue is not None:
        queue.put(play_dict)

    return play_dict


if __name__ == "__main__":
    ({1, 4, 47}, **********.485885, 620, 764)
    [[53, 15], 1740667462.375711, 364, 503]
    # tickets = simulate_tickets()

    tickets = [{'nap': [[11, 90], '3n96469&3163', 0.0, 100.0]}, {'nap': [[1, 90], '3n96469&3164', 0.0, 100.0]}, {'nap': [[62, 16], 'i469895&3173', 0.0, 500.0]}, {'nap': [[89, 1], '4380f78&3178', 0.0, 100.0]}, {'nap': [[1, 89], '86232k0&3218', 0.0, 30.0]}, {'nap': [[1, 90], '86232k0&3219', 0.0, 30.0]}, {'nap': [[19, 90], '86232k0&3220', 0.0, 30.0]}, {'nap': [[78, 90], 'r167061&3240', 0.0, 50.0]}, {'against': [[[31, 40, 51, 68, 76], [1, 2, 3, 16, 22]], '1057d54&3242', 0.0, 125.0]}, {'against': [[[20, 18, 30, 40, 36], [11, 1, 9, 60, 33]], '241n467&3266', 0.0, 125.0]}, {'against': [[[27, 19, 18, 32, 43], [48, 60, 38, 53, 31]], '86e4927&3267', 0.0, 125.0]}, {'against': [[[2, 45, 37, 65, 15], [34, 90, 66, 12, 44]], '410y685&3269', 0.0, 125.0]}, {'perm-2': [[58, 46, 31, 34, 1, 33, 37, 38, 39, 40], '67889f2&3270', 50.0, 2250.0]}, {'against': [[[24, 30, 14, 39, 35], [56, 42, 53, 46, 40]], '725f970&3272', 0.0, 125.0]}, {'perm-2': [[1, 15, 45, 44, 66], 'l727133&3273', 10.0, 100.0]}, {'perm-2': [[13, 31, 14, 41, 4], '735726p&3179', 50.0, 500.0]}, {'nap': [[89, 60], '49865y4&3180', 0.0, 50.0]}, {'perm-2': [[89, 60, 66], '49865y4&3181', 50.0, 150.0]}, {'perm-2': [[60, 70, 80], '882086m&3182', 200.0, 600.0]}, {'nap': [[10, 90], '553k636&3183', 0.0, 50.0]}, {'perm-2': [[60, 70, 80], '553k636&3184', 50.0, 150.0]}, {'perm-2': [[13, 52, 56], '98p8131&3185', 200.0, 600.0]}, {'nap': [[5, 15], '2g12777&3187', 0.0, 100.0]}, {'perm-2': [[13, 54, 87, 23], '2g12777&3188', 50.0, 300.0]}, {'nap': [[10, 11], '8i04170&3189', 0.0, 25.0]}, {'nap': [[10, 33], '8i04170&3190', 0.0, 25.0]}, {'nap': [[33, 3], '8i04170&3191', 0.0, 25.0]}, {'perm-2': [[66, 89, 60], 'l200971&3193', 100.0, 300.0]}, {'nap': [[10, 20], '093i569&3196', 0.0, 25.0]}, {'nap': [[3, 10], '8i04170&3192', 0.0, 25.0]}, {'perm-2': [[66, 60, 89, 90, 87], 'l200971&3194', 20.0, 200.0]}, {'nap': [[1, 19], '093i569&3195', 0.0, 25.0]}, {'nap': [[90, 16], '99196h7&3221', 0.0, 50.0]}, {'nap': [[32, 90], '99196h7&3222', 0.0, 100.0]}, {'nap': [[61, 41], '99196h7&3223', 0.0, 50.0]}, {'against': [[[16, 14, 11, 10, 65], [21, 12, 15, 1, 2]], '40y0042&3227', 0.0, 125.0]}, {'perm-2': [[10, 11, 19, 33], '850928b&3228', 50.0, 300.0]}, {'nap': [[33, 19], '850928b&3229', 0.0, 150.0]}, {'nap': [[11, 10], '850928b&3230', 0.0, 50.0]}, {'perm-2': [[34, 48, 90], '19988j3&3231', 35.0, 105.0]}, {'nap': [[6, 1], '093i569&3197', 0.0, 25.0]}, {'nap': [[2, 6], '093i569&3198', 0.0, 25.0]}, {'nap': [[33, 49], '093i569&3199', 0.0, 25.0]}, {'nap': [[19, 9], '093i569&3200', 0.0, 25.0]}, {'perm-2': [[66, 90, 89, 60], '673129v&3201', 50.0, 300.0]}, {'perm-2': [[31, 90, 1, 2, 3, 15, 5, 4, 47, 53], '61e6250&3235', 5.0, 225.0]}, {'nap': [[34, 48], '246326e&3245', 0.0, 500.0]}, {'nap': [[34, 70], '8708r62&3246', 0.0, 500.0]}, {'against': [[[45, 34, 50, 21, 30], [8, 16, 65, 40, 55]], '29577e9&3247', 0.0, 125.0]}, {'perm-2': [[33, 34, 48, 43], '13808b6&3248', 20.0, 120.0]}, {'perm-2': [[10, 11, 1, 90, 43, 45, 33, 34], '13808b6&3249', 5.0, 140.0]}, {'nap': [[90, 1], '13808b6&3250', 0.0, 150.0]}, {'against': [[[3, 5, 12, 10, 19], [29, 34, 15, 47, 8]], '8175a43&3251', 0.0, 125.0]}, {'perm-2': [[60, 90, 80], '96451i1&3252', 20.0, 60.0]}, {'perm-2': [[60, 80, 90], '96451i1&3253', 20.0, 60.0]}, {'nap': [[48, 34], '96451i1&3254', 0.0, 30.0]}, {'against': [[[3, 34, 15, 19, 29], [1, 2, 4, 7, 11]], '860y315&3255', 0.0, 125.0]}, {'nap': [[34, 48], '4g93521&3256', 0.0, 100.0]}, {'nap': [[70, 80, 60], '17045c1&3202', 0.0, 200.0]}, {'perm-2': [[34, 60, 70, 80, 35], '81855s9&3203', 50.0, 500.0]}, {'nap': [[34, 50], '667727r&3204', 0.0, 100.0]}, {'perm-2': [[33, 62, 50, 39, 10], '1948n59&3205', 50.0, 500.0]}, {'against': [[[1, 2, 3, 4, 5], [45, 66, 65, 15, 6]], '60871f1&3206', 0.0, 125.0]}, {'perm-2': [[1, 2, 3, 4, 5], '69201l6&3207', 15.0, 150.0]}, {'against': [[[15, 1, 66, 45, 44], [54, 4, 51, 21, 10]], '268m381&3208', 0.0, 125.0]}, {'nap': [[34, 47], '407965a&3210', 0.0, 200.0]}, {'nap': [[34, 38], '407965a&3211', 0.0, 200.0]}, {'nap': [[34, 39], '407965a&3212', 0.0, 200.0]}, {'against': [[[65, 67, 54, 44, 15], [1, 2, 3, 4, 5]], '71m0628&3232', 0.0, 125.0]}, {'against': [[[1, 2, 3, 4, 5], [44, 45, 11, 33, 66]], '853r119&3233', 0.0, 125.0]}, {'against': [[[1, 2, 3, 4, 11], [16, 21, 22, 15, 41]], '7q02301&3234', 0.0, 125.0]}, {'nap': [[34, 48], 'r167061&3236', 0.0, 50.0]}, {'perm-2': [[63, 64, 45, 79, 78, 61], 'r167061&3237', 15.0, 225.0]}, {'nap': [[63, 64], 'r167061&3238', 0.0, 50.0]}, {'against': [[[9, 6, 21, 90, 1], [4, 15, 12, 37, 60]], '6753g86&3241', 0.0, 125.0]}, {'against': [[[30, 27, 25, 10, 12], [49, 36, 66, 80, 35]], '1q54303&3257', 0.0, 125.0]}, {'nap': [[45, 54], 'r167061&3239', 0.0, 25.0]}, {'nap': [[34, 33], '407965a&3209', 0.0, 200.0]}, {'perm-2': [[10, 11, 33, 90, 19], 'h707750&3224', 5.0, 50.0]}, {'nap': [[10, 11], 'h707750&3225', 0.0, 25.0]}, {'nap': [[11, 90], 'h707750&3226', 0.0, 25.0]}, {'nap': [[34, 48], 'x925070&3258', 0.0, 100.0]}, {'nap': [[80, 81], '58641u8&3259', 0.0, 1000.0]}, {'perm-2': [[63, 64, 45, 34, 48], '454687d&3261', 18.0, 180.0]}, {'nap': [[63, 64], '454687d&3262', 0.0, 25.0]}, {'nap': [[63, 45], '454687d&3263', 0.0, 25.0]}, {'nap': [[45, 63], '454687d&3264', 0.0, 25.0]}, {'nap': [[34, 48], '454687d&3260', 0.0, 25.0]}, {'against': [[[8, 16, 25, 43, 60], [90, 69, 85, 19, 80]], '6e11187&3265', 0.0, 125.0]}, {'against': [[[17, 25, 28, 10, 1], [40, 70, 63, 90, 14]], '3310x47&3268', 0.0, 125.0]}, {'perm-2': [[60, 62, 70, 76, 80], '67889f2&3271', 100.0, 1000.0]}, {'nap': [[90, 1], 'x082518&3328', 0.0, 50.0]}, {'nap': [[46, 47], '86232k0&3213', 0.0, 30.0]}, {'nap': [[49, 50], '86232k0&3214', 0.0, 30.0]}, {'nap': [[76, 67], '86232k0&3215', 0.0, 30.0]}, {'nap': [[23, 90], '86232k0&3216', 0.0, 30.0]}, {'nap': [[32, 90], '86232k0&3217', 0.0, 30.0]}, {'perm-2': [[74, 35, 61, 89, 84, 87, 83, 49, 12, 15], '534c902&3165', 10.0, 450.0]}, {'nap': [[61, 89], '534c902&3166', 0.0, 100.0]}, {'nap': [[74, 35], '534c902&3167', 0.0, 50.0]}, {'nap': [[87, 49], '534c902&3168', 0.0, 100.0]}, {'perm-2': [[2, 6, 13], '592w160&3169', 100.0, 300.0]}, {'perm-2': [[13, 2, 6, 62, 89, 31], '592w160&3170', 20.0, 300.0]}, {'perm-2': [[80, 82, 83], '592w160&3171', 100.0, 300.0]}, {'perm-2': [[2, 6, 13], '592w160&3172', 50.0, 150.0]}, {'nap': [[82, 83], 'i469895&3174', 0.0, 200.0]}, {'perm-2': [[87, 83, 49, 12, 10, 11], 'g484790&3175', 20.0, 300.0]}, {'perm-2': [[1, 90, 89, 31, 13], '4380f78&3176', 30.0, 300.0]}, {'nap': [[1, 90], '4380f78&3177', 0.0, 100.0]}, {'against': [[[1, 2, 3, 4, 5], [6, 7, 8, 9, 10]], '283y382&3186', 0.0, 125.0]}, {'perm-2': [[1, 15, 44, 45, 66], '21407f3&3274', 10.0, 100.0]}, {'perm-2': [[1, 2, 4, 5, 3], '481145e&3280', 10.0, 100.0]}, {'perm-2': [[36, 37, 38, 39, 40], '4416b39&3283', 10.0, 100.0]}, {'nap': [[38, 83], '860292j&3284', 0.0, 50.0]}, {'perm-2': [[34, 48, 43, 84, 69], '860292j&3285', 30.0, 300.0]}, {'nap': [[48, 34], '860292j&3286', 0.0, 50.0]}, {'nap': [[90, 1], '860292j&3287', 0.0, 50.0]}, {'nap': [[33, 90], '860292j&3288', 0.0, 50.0]}, {'perm-2': [[36, 37, 38, 39, 40], '0y36973&3292', 10.0, 100.0]}, {'perm-2': [[1, 15, 44, 45, 66], '07259w7&3275', 10.0, 100.0]}, {'perm-2': [[1, 2, 3, 4, 5], '385634j&3278', 10.0, 100.0]}, {'nap': [[19, 9], 'x082518&3329', 0.0, 50.0]}, {'nap': [[16, 55], 'x082518&3330', 0.0, 25.0]}, {'perm-2': [[1, 15, 45, 44, 66], 'r302167&3276', 10.0, 100.0]}, {'perm-2': [[1, 15, 44, 45, 66], '74922r8&3277', 10.0, 100.0]}, {'perm-2': [[1, 2, 3, 4, 5], 'g335355&3279', 10.0, 100.0]}, {'perm-2': [[1, 2, 3, 4, 5], 'p802788&3281', 10.0, 100.0]}, {'perm-2': [[1, 2, 3, 4, 5], 'p005894&3282', 10.0, 100.0]}, {'perm-2': [[36, 37, 38, 39, 40], '37p8260&3289', 10.0, 100.0]}, {'perm-2': [[36, 37, 38, 39, 40], '035o665&3290', 10.0, 100.0]}, {'perm-2': [[60, 9, 8, 19], '5166g23&3298', 30.0, 180.0]}, {'perm-2': [[24, 28, 14, 50, 45], '5166g23&3299', 20.0, 200.0]}, {'perm-2': [[55, 50, 5], '5166g23&3300', 50.0, 150.0]}, {'perm-2': [[17, 18, 19], '5166g23&3301', 50.0, 150.0]}, {'perm-2': [[14, 41, 49], '5166g23&3302', 50.0, 150.0]}, {'perm-2': [[31, 41, 13, 14], '5166g23&3303', 30.0, 180.0]}, {'perm-2': [[31, 14, 13], '5166g23&3304', 100.0, 300.0]}, {'nap': [[64, 68], '1695s38&3310', 0.0, 200.0]}, {'nap': [[36, 87], '0r40946&3342', 0.0, 500.0]}, {'perm-2': [[9, 2, 22, 27, 51], '12g0223&3343', 10.0, 100.0]}, {'perm-2': [[36, 37, 38, 39, 40], 'c701750&3291', 10.0, 100.0]}, {'nap': [[60, 80], 'h903187&3296', 0.0, 100.0]}, {'nap': [[60, 70], '39e5298&3297', 0.0, 100.0]}, {'nap': [[87, 36], '702v080&3316', 0.0, 200.0]}, {'nap': [[1, 45], '836b945&3293', 0.0, 100.0]}, {'nap': [[1, 44], '5657o33&3294', 0.0, 100.0]}, {'against': [[[14, 16, 44, 50, 39], [40, 58, 48, 62, 81]], '0x10216&3295', 0.0, 125.0]}, {'against': [[[37, 43, 38, 56, 48], [32, 53, 55, 46, 63]], '84g8490&3308', 0.0, 125.0]}, {'nap': [[38, 32], '97499u5&3311', 0.0, 200.0]}, {'nap': [[90, 1], '41i7191&3305', 0.0, 100.0]}, {'against': [[[49, 16, 10, 11, 23], [44, 45, 65, 90, 2]], '10503r2&3318', 0.0, 125.0]}, {'nap': [[87, 21], '992672r&3323', 0.0, 25.0]}, {'perm-2': [[9, 90, 32, 24, 40], '3t33461&3307', 20.0, 200.0]}, {'nap': [[90, 1], '3t33461&3306', 0.0, 50.0]}, {'nap': [[34, 48], '99980n1&3309', 0.0, 100.0]}, {'nap': [[34, 48], '96l6204&3312', 0.0, 50.0]}, {'perm-2': [[13, 31, 79, 34, 9, 42], '96l6204&3313', 20.0, 300.0]}, {'perm-2': [[19, 78, 64, 33], '96l6204&3314', 25.0, 150.0]}, {'perm-2': [[47, 53, 59, 27, 28, 60, 90, 81, 82, 66], '56753k6&3315', 5.0, 225.0]}, {'against': [[[49, 16, 10, 11, 23], [44, 45, 65, 90, 2]], '5721x46&3317', 0.0, 125.0]}, {'perm-2': [[34, 44, 23, 60, 58], '4138d88&3319', 10.0, 100.0]}, {'nap': [[50, 5], 'x082518&3331', 0.0, 25.0]}, {'nap': [[14, 80], '280u132&3337', 0.0, 50.0]}, {'perm-2': [[5, 19, 28, 89, 68], '1u46660&3345', 10.0, 100.0]}, {'perm-2': [[7, 17, 48, 90, 46], '94651j7&3346', 10.0, 100.0]}, {'perm-2': [[26, 28, 88], '992672r&3324', 50.0, 150.0]}, {'nap': [[61, 6], '992672r&3325', 0.0, 25.0]}, {'nap': [[11, 71], '992672r&3326', 0.0, 50.0]}, {'nap': [[88, 11], '992672r&3327', 0.0, 50.0]}, {'nap': [[79, 69], 'x082518&3332', 0.0, 25.0]}, {'nap': [[51, 81], 'x082518&3335', 0.0, 25.0]}, {'nap': [[10, 11], '992672r&3321', 0.0, 50.0]}, {'nap': [[20, 88], '992672r&3322', 0.0, 50.0]}, {'nap': [[13, 79], '992672r&3320', 0.0, 25.0]}, {'nap': [[76, 67], 'x082518&3333', 0.0, 25.0]}, {'nap': [[71, 26], 'x082518&3334', 0.0, 25.0]}, {'nap': [[80, 81], 'x082518&3336', 0.0, 50.0]}, {'perm-2': [[32, 41, 45, 52, 82], '792h345&3352', 10.0, 100.0]}, {'perm-2': [[70, 65, 43], '280u132&3339', 25.0, 75.0]}, {'nap': [[75, 49], '280u132&3340', 0.0, 50.0]}, {'perm-2': [[10, 32, 29, 26, 54], '280u132&3341', 5.0, 50.0]}, {'nap': [[20, 11], '280u132&3338', 0.0, 25.0]}, {'perm-2': [[89, 76, 39, 20, 2], '565p679&3344', 10.0, 100.0]}, {'perm-2': [[12, 24, 29, 34, 75], '8511z85&3347', 10.0, 100.0]}, {'perm-2': [[14, 20, 39, 58, 82], '7772a84&3348', 10.0, 100.0]}, {'perm-2': [[18, 22, 12, 5, 7], '0u97461&3349', 10.0, 100.0]}, {'perm-2': [[15, 23, 45, 41, 49, 63, 20, 22, 34, 35], '99q2946&3350', 5.0, 225.0]}, {'perm-2': [[3, 10, 24, 52, 68], '411459c&3351', 10.0, 100.0]}, {'perm-2': [[78, 80, 67, 52, 38], '403844w&3353', 10.0, 100.0]}, {'perm-2': [[25, 74, 53, 35, 11, 28, 27, 19, 82, 47, 72, 12, 52, 77], '09k1227&3354', 10.0, 910.0]}, {'nap': [[74, 25], '09k1227&3355', 0.0, 50.0]}, {'nap': [[35, 53], '09k1227&3356', 0.0, 50.0]}, {'nap': [[47, 27], '09k1227&3357', 0.0, 150.0]}, {'nap': [[11, 28], '09k1227&3358', 0.0, 50.0]}, {'nap': [[19, 52], '09k1227&3359', 0.0, 50.0]}, {'nap': [[47, 19], '09k1227&3360', 0.0, 50.0]}, {'perm-2': [[18, 6, 27, 33, 35], 'e927495&3361', 20.0, 200.0]}, {'against': [[[55, 77, 31, 2, 1], [4, 5, 6, 7, 8]], '3270c56&3362', 0.0, 125.0]}, {'perm-2': [[2, 3, 14, 22, 42], '66u8795&3363', 10.0, 100.0]}, {'nap': [[52, 35], '413f379&3364', 0.0, 40.0]}, {'nap': [[19, 27], '413f379&3365', 0.0, 50.0]}, {'nap': [[11, 35], '413f379&3366', 0.0, 50.0]}, {'nap': [[53, 47], '413f379&3367', 0.0, 50.0]}]

    pprint(tickets)

    least_numbers, _ = count_tickets(tickets=tickets)
    print("LEAST::", least_numbers)

    least_combinations = generate_combinations(least_numbers, 5)

    process_list = []
    result_queue = Queue()

    chunks = chunk_list(least_combinations, 10)

    for chunk in chunks:
        process = Process(target=draw, args=(chunk, tickets, result_queue,))
        process_list.append(process)
        process.start()

    # Collect results from all processes
    play_dict = {}
    for _ in process_list:
        play_dict.update(result_queue.get())  # Combine results

    # Wait for all processes to complete
    for _process in process_list:
        _process.join()

    rtp = 125000
    # Filter out zero values
    non_zero_data = {k: v for k, v in play_dict.items()} # if v[1] > 5 and v[0] < rtp}

    non_zero_data_sort = list(sorted(non_zero_data.items(), key=lambda x: x[1][1]))

    # pprint(non_zero_data_sort, )
    # print("\n")
    # Find the combination with the lowest non-zero give-out amount

    # Filter combos that do not exceed the max give out amount
    filtered_data = [item for item in non_zero_data_sort if item[1][0] <= rtp]
    pprint(filtered_data)

    # Find the combo with the highest number of winners and lowest total winnings
    if filtered_data:
        best_combo = min(filtered_data, key=lambda x: (-x[1][1], x[1][0]))

        print("Best combo:", best_combo[0])
        print("Total winnings:", best_combo[1][0])
        print("Number of winnera:", best_combo[1][1])
    else:
        print("No validated combo within the max give out amount.")

    print("Combination with the Lowest Non-Zero Give-Out Amount:")
    pprint({best_combo: 0})

    # winners = process_tickets(tickets, best_combo[0], return_all=True)
    # pprint(winners)
