#!/usr/bin/env python3
"""
Test runner for all lottery draw algorithm tests
"""

import subprocess
import sys
import time
import requests
import json
import os
from datetime import datetime

class TestRunner:
    def __init__(self):
        self.results = {}
        self.server_url = "http://localhost:5001"
        
    def check_server_running(self):
        """Check if the server is running"""
        try:
            response = requests.get(f"{self.server_url}/", timeout=5)
            return True
        except:
            return False
    
    def run_test_suite(self, test_file, description):
        """Run a specific test suite"""
        print(f"\n{'='*60}")
        print(f"Running {description}")
        print(f"{'='*60}")
        
        start_time = time.time()
        
        try:
            result = subprocess.run([
                sys.executable, test_file
            ], capture_output=True, text=True, timeout=600)  # 10 minute timeout
            
            duration = time.time() - start_time
            
            self.results[test_file] = {
                'description': description,
                'success': result.returncode == 0,
                'duration': duration,
                'stdout': result.stdout,
                'stderr': result.stderr
            }
            
            if result.returncode == 0:
                print(f"✅ PASSED in {duration:.2f}s")
            else:
                print(f"❌ FAILED in {duration:.2f}s")
                print("STDERR:", result.stderr[-500:])  # Last 500 chars
                
        except subprocess.TimeoutExpired:
            print(f"⏰ TIMEOUT after 10 minutes")
            self.results[test_file] = {
                'description': description,
                'success': False,
                'duration': 600,
                'error': 'Timeout'
            }
        except Exception as e:
            print(f"💥 ERROR: {e}")
            self.results[test_file] = {
                'description': description,
                'success': False,
                'duration': 0,
                'error': str(e)
            }
    
    def generate_report(self):
        """Generate a comprehensive test report"""
        report = []
        report.append("LOTTERY DRAW ALGORITHM TEST REPORT")
        report.append("=" * 50)
        report.append(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("")
        
        total_tests = len(self.results)
        passed_tests = sum(1 for r in self.results.values() if r['success'])
        total_duration = sum(r['duration'] for r in self.results.values())
        
        report.append("SUMMARY")
        report.append("-" * 20)
        report.append(f"Total Test Suites: {total_tests}")
        report.append(f"Passed: {passed_tests}")
        report.append(f"Failed: {total_tests - passed_tests}")
        report.append(f"Success Rate: {passed_tests/total_tests*100:.1f}%")
        report.append(f"Total Duration: {total_duration:.2f}s")
        report.append("")
        
        report.append("DETAILED RESULTS")
        report.append("-" * 30)
        
        for test_file, result in self.results.items():
            status = "✅ PASS" if result['success'] else "❌ FAIL"
            report.append(f"{status} {result['description']}")
            report.append(f"    File: {test_file}")
            report.append(f"    Duration: {result['duration']:.2f}s")
            
            if not result['success']:
                if 'error' in result:
                    report.append(f"    Error: {result['error']}")
                if 'stderr' in result and result['stderr']:
                    report.append(f"    Details: {result['stderr'][-200:]}")
            report.append("")
        
        return "\n".join(report)
    
    def run_all_tests(self):
        """Run all test suites"""
        print("🚀 Starting Comprehensive Test Suite for Lottery Draw Algorithm")
        print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # Check if server is running for integration tests
        server_running = self.check_server_running()
        if server_running:
            print("✅ Server detected at localhost:5001")
        else:
            print("⚠️  Server not detected - integration tests will be skipped")
        
        # Test suites to run
        test_suites = [
            ("test_draw_algorithm.py", "Core Algorithm Unit Tests"),
        ]
        
        # Add integration tests only if server is running
        if server_running:
            test_suites.extend([
                ("test_api_integration.py", "API Integration Tests"),
                ("test_performance.py", "Performance & Load Tests"),
            ])
        
        # Run each test suite
        for test_file, description in test_suites:
            if os.path.exists(test_file):
                self.run_test_suite(test_file, description)
            else:
                print(f"⚠️  Test file {test_file} not found - skipping")
        
        # Generate and save report
        report = self.generate_report()
        
        # Save report to file
        with open("test_report.txt", "w") as f:
            f.write(report)
        
        # Print summary
        print("\n" + "="*60)
        print("TEST EXECUTION COMPLETE")
        print("="*60)
        print(report)
        
        # Return overall success
        return all(r['success'] for r in self.results.values())

def main():
    """Main entry point"""
    runner = TestRunner()
    
    # Check for required dependencies
    try:
        import requests
        import psutil
    except ImportError as e:
        print(f"❌ Missing required dependency: {e}")
        print("Install with: pip install requests psutil")
        sys.exit(1)
    
    success = runner.run_all_tests()
    
    if success:
        print("\n🎉 ALL TESTS PASSED!")
        sys.exit(0)
    else:
        print("\n💥 SOME TESTS FAILED!")
        print("Check test_report.txt for detailed results")
        sys.exit(1)

if __name__ == "__main__":
    main()
