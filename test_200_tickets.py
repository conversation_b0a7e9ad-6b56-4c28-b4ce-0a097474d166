#!/usr/bin/env python3

import json
import requests
import time

def test_200_tickets():
    """Test the 200-ticket dataset with the optimized server"""
    
    print("Testing 200-Ticket Dataset Performance")
    print("=" * 60)
    
    # Load the dataset
    try:
        with open("test_dataset_200_tickets.json", "r") as f:
            dataset = json.load(f)
    except FileNotFoundError:
        print("❌ Dataset file not found. Run generate_200_tickets.py first.")
        return
    
    print(f"Loaded dataset with {len(dataset['tickets'])} tickets")
    print(f"Pool value: {dataset['pool_value']:,}")
    
    # Test with different pool values to see performance impact
    test_scenarios = [
        {"name": "Large Pool (2M)", "pool_value": 2000000},
        {"name": "Medium Pool (1M)", "pool_value": 1000000},
        {"name": "Small Pool (500K)", "pool_value": 500000},
    ]
    
    for scenario in test_scenarios:
        print(f"\n{'-' * 40}")
        print(f"Testing: {scenario['name']}")
        print(f"Pool Value: {scenario['pool_value']:,}")
        print(f"{'-' * 40}")
        
        # Prepare request data
        test_data = {
            "tickets": dataset['tickets'],
            "pool_value": scenario['pool_value']
        }
        
        # Measure performance
        start_time = time.time()
        
        try:
            response = requests.post(
                "http://localhost:5001/nap2_turbo",
                json=test_data,
                timeout=120  # 2 minute timeout for large dataset
            )
            
            end_time = time.time()
            duration = end_time - start_time
            
            if response.status_code == 200:
                result = response.json()
                
                print(f"⏱️  Duration: {duration:.2f} seconds")
                print(f"✅ Success!")
                print(f"📊 Results:")
                print(f"   Winners: {len(result['winners'])}")
                print(f"   Total winnings: {result['total_winnings']:,}")
                print(f"   First numbers: {result['first_numbers']}")
                print(f"   Last numbers: {result['last_numbers']}")
                
                # Performance rating
                if duration < 5.0:
                    print("🚀 EXCELLENT performance")
                elif duration < 15.0:
                    print("✅ GOOD performance")
                elif duration < 30.0:
                    print("⚠️  ACCEPTABLE performance")
                elif duration < 60.0:
                    print("🐌 SLOW performance")
                else:
                    print("🚨 VERY SLOW performance")
                
                # Analyze winners by turbo type
                winner_analysis = {}
                for winner in result['winners']:
                    game_type = winner['ticket']['game_type']
                    if game_type not in winner_analysis:
                        winner_analysis[game_type] = {'count': 0, 'total_winnings': 0}
                    winner_analysis[game_type]['count'] += 1
                    winner_analysis[game_type]['total_winnings'] += winner['winnings']
                
                if winner_analysis:
                    print(f"📈 Winner breakdown:")
                    for game_type, stats in sorted(winner_analysis.items()):
                        print(f"   {game_type}: {stats['count']} winners, {stats['total_winnings']:,} winnings")
                
            else:
                print(f"❌ HTTP Error {response.status_code}")
                try:
                    error_data = response.json()
                    print(f"Error: {error_data}")
                except:
                    print(f"Response: {response.text[:200]}...")
                    
        except requests.exceptions.Timeout:
            print("⏰ TIMEOUT - Request took longer than 2 minutes")
        except requests.exceptions.ConnectionError:
            print("❌ CONNECTION ERROR - Server not responding")
        except Exception as e:
            print(f"❌ Error: {e}")
    
    print(f"\n{'=' * 60}")
    print("200-Ticket Performance Test Completed!")

if __name__ == "__main__":
    test_200_tickets()
