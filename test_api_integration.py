#!/usr/bin/env python3
"""
Integration tests for lottery API endpoints
"""

import unittest
import requests
import json
import time
from concurrent.futures import ThreadPoolExecutor

class TestAPIIntegration(unittest.TestCase):
    """Test API endpoints with real server"""
    
    BASE_URL = "http://localhost:5001"
    
    def setUp(self):
        """Set up test data"""
        self.standard_tickets = [
            {'nap': ([1, 2, 3], 1234567890, 100, 100)},
            {'perm-3': ([4, 5, 6, 7], 1234567890, 50, 200)},
            {'against': (([8, 9], [10, 11, 12]), 1234567890, 75, 150)}
        ]
        
        self.nap2_turbo_tickets = [
            {
                "game_id": "TEST0001",
                "game_type": "turbo-3", 
                "stake_amount": 100,
                "numbers": [1, 2, 3],
                "position": "first"
            },
            {
                "game_id": "TEST0002",
                "game_type": "turbo-4",
                "stake_amount": 200, 
                "numbers": [4, 5, 6, 7],
                "position": "last"
            }
        ]

    def test_simulate_endpoint_basic(self):
        """Test basic /simulate endpoint functionality"""
        data = {
            "tickets": self.standard_tickets,
            "rtp": 50000,
            "lottery_type": None,
            "double_chance": False
        }
        
        response = requests.post(f"{self.BASE_URL}/simulate", json=data, timeout=30)
        
        self.assertEqual(response.status_code, 200)
        result = response.json()
        
        # Check required fields
        self.assertIn('best_combo', result)
        self.assertIn('total_winnings', result)
        self.assertIn('number_of_winners', result)
        self.assertIn('winners', result)

    def test_simulate_endpoint_double_chance(self):
        """Test /simulate endpoint with double chance"""
        data = {
            "tickets": self.standard_tickets,
            "rtp": 50000,
            "lottery_type": None,
            "double_chance": True,
            "double_chance_picks": [1, 2, 3, 4, 5]
        }
        
        response = requests.post(f"{self.BASE_URL}/simulate", json=data, timeout=60)
        
        self.assertEqual(response.status_code, 200)
        result = response.json()
        
        # Should have double chance results
        self.assertIn('double_chance', result)
        self.assertIsNotNone(result['double_chance'])

    def test_nap2_turbo_endpoint(self):
        """Test /nap2_turbo endpoint"""
        data = {
            "tickets": self.nap2_turbo_tickets,
            "pool_value": 100000
        }
        
        response = requests.post(f"{self.BASE_URL}/nap2_turbo", json=data, timeout=30)
        
        self.assertEqual(response.status_code, 200)
        result = response.json()
        
        # Check required fields
        self.assertIn('first_numbers', result)
        self.assertIn('last_numbers', result)
        self.assertIn('total_winnings', result)
        self.assertIn('winners', result)
        
        # Validate number ranges
        self.assertEqual(len(result['first_numbers']), 5)
        self.assertEqual(len(result['last_numbers']), 5)
        self.assertTrue(all(1 <= n <= 90 for n in result['first_numbers']))
        self.assertTrue(all(1 <= n <= 90 for n in result['last_numbers']))

    def test_input_validation(self):
        """Test API input validation"""
        # Test missing tickets
        response = requests.post(f"{self.BASE_URL}/simulate", json={}, timeout=10)
        self.assertEqual(response.status_code, 400)
        
        # Test invalid ticket format for NAP2 turbo
        invalid_data = {
            "tickets": [{"invalid": "ticket"}],
            "pool_value": 10000
        }
        response = requests.post(f"{self.BASE_URL}/nap2_turbo", json=invalid_data, timeout=10)
        self.assertEqual(response.status_code, 400)

    def test_performance_benchmarks(self):
        """Test API performance benchmarks"""
        # Small dataset
        small_data = {
            "tickets": self.standard_tickets[:1],
            "rtp": 10000
        }
        
        start_time = time.time()
        response = requests.post(f"{self.BASE_URL}/simulate", json=small_data, timeout=30)
        small_duration = time.time() - start_time
        
        self.assertEqual(response.status_code, 200)
        self.assertLess(small_duration, 5.0)  # Should be very fast
        
        # Medium dataset
        medium_tickets = self.standard_tickets * 10  # 30 tickets
        medium_data = {
            "tickets": medium_tickets,
            "rtp": 50000
        }
        
        start_time = time.time()
        response = requests.post(f"{self.BASE_URL}/simulate", json=medium_data, timeout=60)
        medium_duration = time.time() - start_time
        
        self.assertEqual(response.status_code, 200)
        self.assertLess(medium_duration, 30.0)  # Should complete within 30s

    def test_concurrent_requests(self):
        """Test handling of concurrent requests"""
        def make_request():
            data = {
                "tickets": self.standard_tickets,
                "rtp": 25000
            }
            response = requests.post(f"{self.BASE_URL}/simulate", json=data, timeout=30)
            return response.status_code == 200
        
        # Test 5 concurrent requests
        with ThreadPoolExecutor(max_workers=5) as executor:
            futures = [executor.submit(make_request) for _ in range(5)]
            results = [future.result() for future in futures]
        
        # All requests should succeed
        self.assertTrue(all(results))

    def test_rtp_compliance_integration(self):
        """Test RTP compliance in real API calls"""
        rtp_values = [10000, 25000, 50000, 100000]
        
        for rtp in rtp_values:
            data = {
                "tickets": self.standard_tickets,
                "rtp": rtp
            }
            
            response = requests.post(f"{self.BASE_URL}/simulate", json=data, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                if 'total_winnings' in result:
                    self.assertLessEqual(result['total_winnings'], rtp,
                                       f"RTP violated for limit {rtp}")

class TestNAP2TurboValidation(unittest.TestCase):
    """Specific tests for NAP2 Turbo validation"""
    
    BASE_URL = "http://localhost:5001"
    
    def test_turbo_game_validation(self):
        """Test turbo game type validation"""
        # Valid turbo-3 ticket
        valid_ticket = {
            "game_id": "VALID001",
            "game_type": "turbo-3",
            "stake_amount": 100,
            "numbers": [1, 2, 3],
            "position": "first"
        }
        
        data = {"tickets": [valid_ticket], "pool_value": 10000}
        response = requests.post(f"{self.BASE_URL}/nap2_turbo", json=data, timeout=10)
        self.assertEqual(response.status_code, 200)
        
        # Invalid: turbo-3 with 4 numbers
        invalid_ticket = {
            "game_id": "INVALID1",
            "game_type": "turbo-3",
            "stake_amount": 100,
            "numbers": [1, 2, 3, 4],  # 4 numbers but turbo-3
            "position": "first"
        }
        
        data = {"tickets": [invalid_ticket], "pool_value": 10000}
        response = requests.post(f"{self.BASE_URL}/nap2_turbo", json=data, timeout=10)
        self.assertEqual(response.status_code, 400)

    def test_winner_consistency(self):
        """Test that winners match the drawn numbers"""
        # Create tickets that should definitely win
        tickets = [
            {
                "game_id": "WIN00001",
                "game_type": "turbo-2",
                "stake_amount": 100,
                "numbers": [1, 2],
                "position": "first"
            }
        ]
        
        data = {"tickets": tickets, "pool_value": 100000}
        response = requests.post(f"{self.BASE_URL}/nap2_turbo", json=data, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            first_numbers = result['first_numbers']
            winners = result['winners']
            
            # Manually verify winner calculation
            ticket = tickets[0]
            relevant_positions = first_numbers[:2]  # First 2 positions for turbo-2
            player_numbers = set(ticket['numbers'])
            drawn_numbers = set(relevant_positions)
            matching = player_numbers.intersection(drawn_numbers)
            
            if len(matching) >= 2:
                # Should have at least 1 winner
                self.assertGreater(len(winners), 0)
            else:
                # Should have no winners
                self.assertEqual(len(winners), 0)

if __name__ == '__main__':
    print("Starting API Integration Tests...")
    print("Make sure the server is running on localhost:5001")
    print("=" * 60)
    
    unittest.main(verbosity=2)
