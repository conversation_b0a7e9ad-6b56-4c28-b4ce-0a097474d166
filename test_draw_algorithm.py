#!/usr/bin/env python3
"""
Comprehensive test suite for the lottery draw algorithm
"""

import unittest
import json
import time
import requests
from unittest.mock import patch, MagicMock
import sys
import os

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from server import (
    handle_draw, process_nap2_turbo, generate_combinations_optimized,
    quick_estimate_winnings, generate_safe_numbers, run_nap2_turbo_draw
)
from number_match import process_tickets, nap_find_matching_pairs, perm_find_matching_pairs
from gen_tickets import count_tickets, generate_combinations

class TestDrawAlgorithmCore(unittest.TestCase):
    """Test core algorithm functions"""

    def setUp(self):
        """Set up test data"""
        self.sample_tickets = [
            {'nap': ([1, 2, 3], 1234567890, 100, 100)},
            {'perm-3': ([4, 5, 6, 7], 1234567890, 50, 200)},
            {'against': (([8, 9], [10, 11, 12]), 1234567890, 75, 150)}
        ]

        self.nap2_turbo_tickets = [
            {
                "game_id": "TEST0001",
                "game_type": "turbo-3",
                "stake_amount": 100,
                "numbers": [1, 2, 3],
                "position": "first"
            },
            {
                "game_id": "TEST0002",
                "game_type": "turbo-4",
                "stake_amount": 200,
                "numbers": [4, 5, 6, 7],
                "position": "last"
            }
        ]

    def test_nap2_turbo_processing(self):
        """Test NAP2 turbo game processing logic"""
        ticket = self.nap2_turbo_tickets[0]
        first_draw = [1, 2, 3, 8, 9]  # Perfect match in first 3 positions
        last_draw = [10, 11, 12, 13, 14]

        winnings, multiplier = process_nap2_turbo(ticket, first_draw, last_draw)

        # Should win with correct order multiplier
        self.assertGreater(winnings, 0)
        self.assertEqual(multiplier, 1200)  # turbo-3 correct order multiplier

    def test_nap2_turbo_incorrect_order(self):
        """Test NAP2 turbo with incorrect order but matching numbers"""
        ticket = self.nap2_turbo_tickets[0]
        first_draw = [3, 1, 2, 8, 9]  # Same numbers, wrong order
        last_draw = [10, 11, 12, 13, 14]

        winnings, multiplier = process_nap2_turbo(ticket, first_draw, last_draw)

        # Should win with incorrect order multiplier
        self.assertGreater(winnings, 0)
        self.assertEqual(multiplier, 400)  # turbo-3 incorrect order multiplier

    def test_nap2_turbo_no_match(self):
        """Test NAP2 turbo with no matching numbers"""
        ticket = self.nap2_turbo_tickets[0]
        first_draw = [10, 11, 12, 13, 14]  # No matching numbers
        last_draw = [15, 16, 17, 18, 19]

        winnings, multiplier = process_nap2_turbo(ticket, first_draw, last_draw)

        # Should not win
        self.assertEqual(winnings, 0)
        self.assertEqual(multiplier, 0)

    def test_combination_optimization(self):
        """Test optimized combination generation"""
        elements = list(range(1, 21))  # 20 elements
        max_combinations = 100

        combinations = generate_combinations_optimized(elements, 5, max_combinations)

        # Should limit combinations
        self.assertLessEqual(len(combinations), max_combinations)
        self.assertGreater(len(combinations), 0)

        # All combinations should be valid
        for combo in combinations:
            self.assertEqual(len(combo), 5)
            self.assertEqual(len(set(combo)), 5)  # No duplicates

    def test_quick_estimation(self):
        """Test quick winnings estimation"""
        combination = [1, 2, 3, 4, 5]
        estimated = quick_estimate_winnings(self.sample_tickets, combination)

        # Should return a reasonable estimate
        self.assertIsInstance(estimated, (int, float))
        self.assertGreaterEqual(estimated, 0)

class TestRTPCompliance(unittest.TestCase):
    """Test RTP (Return to Player) compliance"""

    def test_rtp_never_exceeded(self):
        """Test that RTP limits are never exceeded"""
        tickets = [
            {'nap': ([1, 2], 1234567890, 1000, 1000)},  # High stake ticket
            {'nap': ([3, 4], 1234567890, 1000, 1000)},
        ]
        rtp = 50000  # Low RTP to force constraint

        result = handle_draw(tickets, rtp, None, [])

        if 'total_winnings' in result:
            self.assertLessEqual(result['total_winnings'], rtp)

    def test_winner_count_optimization(self):
        """Test that algorithm optimizes for winner count"""
        tickets = []
        for i in range(10):
            tickets.append({'nap': ([i+1, i+2], 1234567890, 100, 100)})

        rtp = 100000  # High RTP
        result = handle_draw(tickets, rtp, None, [])

        if 'number_of_winners' in result:
            # Should try to maximize winners within RTP
            self.assertGreaterEqual(result['number_of_winners'], 0)

class TestEdgeCases(unittest.TestCase):
    """Test edge cases and error conditions"""

    def test_empty_tickets(self):
        """Test handling of empty ticket list"""
        result = handle_draw([], 10000, None, [])
        self.assertIn('message', result)

    def test_invalid_lottery_type(self):
        """Test handling of different lottery types"""
        tickets = [{'nap': ([1, 2], 1234567890, 100, 100)}]

        # Test k_30 lottery
        result_k30 = handle_draw(tickets, 10000, "k_30", [])
        result_standard = handle_draw(tickets, 10000, None, [])

        # Both should work but may have different results
        self.assertIsInstance(result_k30, dict)
        self.assertIsInstance(result_standard, dict)

    def test_large_ticket_volume(self):
        """Test performance with large ticket volumes"""
        tickets = []
        for i in range(100):  # 100 tickets
            tickets.append({'nap': ([i%10+1, i%10+2], 1234567890, 100, 100)})

        start_time = time.time()
        result = handle_draw(tickets, 100000, None, [])
        duration = time.time() - start_time

        # Should complete within reasonable time (optimized version)
        self.assertLess(duration, 30.0)  # 30 seconds max
        self.assertIsInstance(result, dict)

class TestDataConsistency(unittest.TestCase):
    """Test data consistency and integrity"""

    def test_winner_calculation_consistency(self):
        """Test that winner calculations are consistent"""
        tickets = [
            {'nap': ([1, 2, 3], 1234567890, 100, 100)},
            {'nap': ([4, 5], 1234567890, 200, 200)}
        ]

        # Test with known combination
        pool_numbers = [1, 2, 3, 6, 7]
        winners, total_winners, total_winnings = process_tickets(
            tickets, pool_numbers, 240, return_all=True
        )

        # Verify consistency
        self.assertEqual(len(winners), total_winners)
        calculated_winnings = sum(winner[2] for winner in winners)
        self.assertEqual(calculated_winnings, total_winnings)

    def test_number_range_validation(self):
        """Test that generated numbers are within valid ranges"""
        tickets = [{'nap': ([1, 2], 1234567890, 100, 100)}]

        result = handle_draw(tickets, 10000, None, [])

        if 'best_combo' in result:
            combo = result['best_combo']
            # All numbers should be 1-90 for standard lottery
            self.assertTrue(all(1 <= n <= 90 for n in combo))
            self.assertEqual(len(combo), 5)
            self.assertEqual(len(set(combo)), 5)  # No duplicates

class TestBusinessLogic(unittest.TestCase):
    """Test business logic and game rules"""

    def test_multiplier_accuracy(self):
        """Test that multipliers are calculated correctly"""
        # Test NAP2 turbo multipliers
        test_cases = [
            # (turbo_type, correct_order, expected_multiplier)
            (2, True, 3600),
            (2, False, 1200),
            (3, True, 1200),
            (3, False, 400),
            (4, True, 600),
            (4, False, 200),
            (5, True, 360),
            (5, False, 120)
        ]

        for turbo_type, correct_order, expected_multiplier in test_cases:
            ticket = {
                "game_id": "TEST0001",
                "game_type": f"turbo-{turbo_type}",
                "stake_amount": 100,
                "numbers": list(range(1, turbo_type + 1)),
                "position": "first"
            }

            if correct_order:
                first_draw = list(range(1, turbo_type + 1)) + [10, 11]
            else:
                first_draw = list(range(1, turbo_type + 1))[::-1] + [10, 11]  # Reverse order

            last_draw = [20, 21, 22, 23, 24]

            winnings, multiplier = process_nap2_turbo(ticket, first_draw, last_draw)

            self.assertEqual(multiplier, expected_multiplier,
                           f"Turbo-{turbo_type} {'correct' if correct_order else 'incorrect'} order multiplier mismatch")

    def test_stake_amount_impact(self):
        """Test that stake amounts correctly impact winnings"""
        base_ticket = {
            "game_id": "TEST0001",
            "game_type": "turbo-3",
            "numbers": [1, 2, 3],
            "position": "first"
        }

        first_draw = [1, 2, 3, 8, 9]  # Perfect match
        last_draw = [10, 11, 12, 13, 14]

        stakes = [100, 200, 500, 1000]

        for stake in stakes:
            ticket = base_ticket.copy()
            ticket["stake_amount"] = stake

            winnings, multiplier = process_nap2_turbo(ticket, first_draw, last_draw)
            expected_winnings = stake * 1200  # turbo-3 correct order

            self.assertEqual(winnings, expected_winnings,
                           f"Winnings should scale with stake amount: {stake}")

    def test_position_logic(self):
        """Test first vs last position logic"""
        ticket_first = {
            "game_id": "TEST0001",
            "game_type": "turbo-3",
            "stake_amount": 100,
            "numbers": [1, 2, 3],
            "position": "first"
        }

        ticket_last = {
            "game_id": "TEST0002",
            "game_type": "turbo-3",
            "stake_amount": 100,
            "numbers": [1, 2, 3],
            "position": "last"
        }

        first_draw = [1, 2, 3, 8, 9]
        last_draw = [10, 11, 12, 13, 14]

        # First position ticket should win
        winnings_first, _ = process_nap2_turbo(ticket_first, first_draw, last_draw)
        self.assertGreater(winnings_first, 0)

        # Last position ticket should not win (numbers not in last draw)
        winnings_last, _ = process_nap2_turbo(ticket_last, first_draw, last_draw)
        self.assertEqual(winnings_last, 0)

class TestRegressionTests(unittest.TestCase):
    """Regression tests for known issues"""

    def test_pool_constraint_consistency(self):
        """Test the pool constraint issue from test_pool_constraint_issue.py"""
        # This was a known issue where winners were shown but payouts didn't match
        tickets = [
            {
                "game_id": "TEST0001",
                "game_type": "turbo-3",
                "stake_amount": 100,
                "numbers": [1, 2, 3],
                "position": "first"
            },
            {
                "game_id": "TEST0002",
                "game_type": "turbo-4",
                "stake_amount": 200,
                "numbers": [1, 2, 3, 4],
                "position": "first"
            }
        ]

        # Simulate a scenario that should create winners
        first_draw = [1, 2, 3, 4, 5]
        last_draw = [10, 11, 12, 13, 14]

        total_winnings = 0
        winner_count = 0

        for ticket in tickets:
            winnings, multiplier = process_nap2_turbo(ticket, first_draw, last_draw)
            if winnings > 0:
                winner_count += 1
                total_winnings += winnings

        # Both tickets should win (perfect matches)
        self.assertEqual(winner_count, 2)
        self.assertGreater(total_winnings, 0)

        # Verify individual calculations
        expected_winnings_1 = 100 * 1200  # turbo-3 correct order
        expected_winnings_2 = 200 * 600   # turbo-4 correct order
        expected_total = expected_winnings_1 + expected_winnings_2

        self.assertEqual(total_winnings, expected_total)

if __name__ == '__main__':
    # Run all tests
    unittest.main(verbosity=2)
