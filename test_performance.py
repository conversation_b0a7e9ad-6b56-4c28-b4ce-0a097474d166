#!/usr/bin/env python3
"""
Performance and load testing for lottery draw algorithm
"""

import unittest
import time
import json
import requests
import statistics
from concurrent.futures import ThreadPoolExecutor, as_completed
import psutil
import os

class TestPerformance(unittest.TestCase):
    """Performance benchmarking tests"""
    
    BASE_URL = "http://localhost:5001"
    
    def setUp(self):
        """Generate test datasets of various sizes"""
        self.small_dataset = self._generate_tickets(10)
        self.medium_dataset = self._generate_tickets(100) 
        self.large_dataset = self._generate_tickets(500)
        self.xlarge_dataset = self._generate_tickets(1000)
    
    def _generate_tickets(self, count):
        """Generate test tickets"""
        tickets = []
        for i in range(count):
            tickets.append({
                'nap': ([i%10+1, i%10+2, i%10+3], 1234567890, 100, 100)
            })
        return tickets
    
    def _generate_nap2_tickets(self, count):
        """Generate NAP2 turbo tickets"""
        tickets = []
        for i in range(count):
            turbo_type = (i % 4) + 2  # 2-5 numbers
            numbers = list(range(i%10+1, i%10+1+turbo_type))
            tickets.append({
                "game_id": f"T{i:07d}",
                "game_type": f"turbo-{turbo_type}",
                "stake_amount": 100,
                "numbers": numbers,
                "position": "first" if i % 2 == 0 else "last"
            })
        return tickets

    def test_algorithm_scalability(self):
        """Test how algorithm performance scales with input size"""
        datasets = [
            ("Small (10)", self.small_dataset),
            ("Medium (100)", self.medium_dataset), 
            ("Large (500)", self.large_dataset),
            ("XLarge (1000)", self.xlarge_dataset)
        ]
        
        results = []
        
        for name, dataset in datasets:
            data = {
                "tickets": dataset,
                "rtp": 100000
            }
            
            # Measure performance
            start_time = time.time()
            start_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB
            
            response = requests.post(f"{self.BASE_URL}/simulate", json=data, timeout=300)
            
            end_time = time.time()
            end_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB
            
            duration = end_time - start_time
            memory_used = end_memory - start_memory
            
            results.append({
                'dataset': name,
                'tickets': len(dataset),
                'duration': duration,
                'memory_mb': memory_used,
                'success': response.status_code == 200
            })
            
            print(f"{name}: {duration:.2f}s, {memory_used:.1f}MB, Success: {response.status_code == 200}")
        
        # Verify performance targets
        for result in results:
            if result['tickets'] <= 100:
                self.assertLess(result['duration'], 10.0, f"Small datasets should complete in <10s")
            elif result['tickets'] <= 500:
                self.assertLess(result['duration'], 60.0, f"Medium datasets should complete in <60s")
            else:
                self.assertLess(result['duration'], 300.0, f"Large datasets should complete in <5min")

    def test_nap2_turbo_performance(self):
        """Test NAP2 turbo performance with different dataset sizes"""
        datasets = [
            (50, "Small NAP2"),
            (200, "Medium NAP2"),
            (500, "Large NAP2")
        ]
        
        for count, name in datasets:
            tickets = self._generate_nap2_tickets(count)
            data = {
                "tickets": tickets,
                "pool_value": 500000
            }
            
            start_time = time.time()
            response = requests.post(f"{self.BASE_URL}/nap2_turbo", json=data, timeout=180)
            duration = time.time() - start_time
            
            print(f"{name} ({count} tickets): {duration:.2f}s, Success: {response.status_code == 200}")
            
            self.assertEqual(response.status_code, 200)
            if count <= 200:
                self.assertLess(duration, 30.0, f"{name} should complete in <30s")
            else:
                self.assertLess(duration, 120.0, f"{name} should complete in <2min")

    def test_optimization_effectiveness(self):
        """Test that optimizations actually improve performance"""
        # Test with optimization limits
        tickets = self._generate_tickets(200)
        
        # Measure multiple runs for statistical significance
        durations = []
        for _ in range(3):
            data = {
                "tickets": tickets,
                "rtp": 75000
            }
            
            start_time = time.time()
            response = requests.post(f"{self.BASE_URL}/simulate", json=data, timeout=120)
            duration = time.time() - start_time
            
            if response.status_code == 200:
                durations.append(duration)
        
        if durations:
            avg_duration = statistics.mean(durations)
            std_duration = statistics.stdev(durations) if len(durations) > 1 else 0
            
            print(f"Average duration: {avg_duration:.2f}s ± {std_duration:.2f}s")
            
            # With optimizations, 200 tickets should complete quickly
            self.assertLess(avg_duration, 45.0, "Optimized algorithm should be fast")

class TestLoadTesting(unittest.TestCase):
    """Load testing with concurrent requests"""
    
    BASE_URL = "http://localhost:5001"
    
    def test_concurrent_load(self):
        """Test server under concurrent load"""
        def make_request(request_id):
            tickets = [
                {'nap': ([request_id%10+1, request_id%10+2], 1234567890, 100, 100)}
            ]
            data = {
                "tickets": tickets,
                "rtp": 25000
            }
            
            start_time = time.time()
            try:
                response = requests.post(f"{self.BASE_URL}/simulate", json=data, timeout=60)
                duration = time.time() - start_time
                return {
                    'success': response.status_code == 200,
                    'duration': duration,
                    'request_id': request_id
                }
            except Exception as e:
                return {
                    'success': False,
                    'duration': time.time() - start_time,
                    'error': str(e),
                    'request_id': request_id
                }
        
        # Test with 10 concurrent requests
        concurrent_requests = 10
        
        with ThreadPoolExecutor(max_workers=concurrent_requests) as executor:
            futures = [executor.submit(make_request, i) for i in range(concurrent_requests)]
            results = [future.result() for future in as_completed(futures)]
        
        # Analyze results
        successful = [r for r in results if r['success']]
        failed = [r for r in results if not r['success']]
        
        success_rate = len(successful) / len(results) * 100
        avg_duration = statistics.mean([r['duration'] for r in successful]) if successful else 0
        
        print(f"Concurrent Load Test Results:")
        print(f"  Requests: {concurrent_requests}")
        print(f"  Success Rate: {success_rate:.1f}%")
        print(f"  Average Duration: {avg_duration:.2f}s")
        print(f"  Failed Requests: {len(failed)}")
        
        # Requirements
        self.assertGreaterEqual(success_rate, 80.0, "Should handle 80%+ of concurrent requests")
        if successful:
            self.assertLess(avg_duration, 30.0, "Average response time should be reasonable")

    def test_memory_usage_stability(self):
        """Test that memory usage remains stable under load"""
        initial_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB
        
        # Make multiple requests
        for i in range(10):
            tickets = [{'nap': ([i+1, i+2], 1234567890, 100, 100)}]
            data = {"tickets": tickets, "rtp": 10000}
            
            response = requests.post(f"{self.BASE_URL}/simulate", json=data, timeout=30)
            self.assertEqual(response.status_code, 200)
        
        final_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB
        memory_growth = final_memory - initial_memory
        
        print(f"Memory usage: {initial_memory:.1f}MB -> {final_memory:.1f}MB (growth: {memory_growth:.1f}MB)")
        
        # Memory growth should be reasonable (less than 100MB for 10 requests)
        self.assertLess(memory_growth, 100.0, "Memory usage should remain stable")

class TestStressScenarios(unittest.TestCase):
    """Stress testing with extreme scenarios"""
    
    BASE_URL = "http://localhost:5001"
    
    def test_high_rtp_scenario(self):
        """Test with very high RTP values"""
        tickets = [{'nap': ([1, 2], 1234567890, 10000, 10000)}]  # High stake
        data = {
            "tickets": tickets,
            "rtp": 10000000  # Very high RTP
        }
        
        response = requests.post(f"{self.BASE_URL}/simulate", json=data, timeout=60)
        self.assertEqual(response.status_code, 200)

    def test_low_rtp_scenario(self):
        """Test with very low RTP values"""
        tickets = [{'nap': ([1, 2], 1234567890, 1000, 1000)}]
        data = {
            "tickets": tickets,
            "rtp": 100  # Very low RTP
        }
        
        response = requests.post(f"{self.BASE_URL}/simulate", json=data, timeout=60)
        # Should either succeed with low winnings or return appropriate message
        self.assertIn(response.status_code, [200])

    def test_edge_case_numbers(self):
        """Test with edge case number combinations"""
        edge_tickets = [
            {'nap': ([1, 90], 1234567890, 100, 100)},  # Min/max numbers
            {'nap': ([45, 46], 1234567890, 100, 100)},  # Middle numbers
            {'nap': ([89, 90], 1234567890, 100, 100)},  # High numbers
        ]
        
        data = {"tickets": edge_tickets, "rtp": 50000}
        response = requests.post(f"{self.BASE_URL}/simulate", json=data, timeout=60)
        self.assertEqual(response.status_code, 200)

if __name__ == '__main__':
    print("Starting Performance and Load Tests...")
    print("Make sure the server is running on localhost:5001")
    print("These tests may take several minutes to complete.")
    print("=" * 60)
    
    unittest.main(verbosity=2)
