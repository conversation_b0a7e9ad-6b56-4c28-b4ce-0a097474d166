#!/usr/bin/env python3

import requests
import json

def test_pool_constraint_issue():
    """Test the pool constraint issue where numbers show winners but payout doesn't match"""
    
    # Create a scenario that should definitely create 2 winners
    data = {
        "tickets": [
            {
                "game_id": "TEST0001",
                "game_type": "turbo-3",
                "stake_amount": 100,
                "numbers": [1, 2, 3],
                "position": "first"
            },
            {
                "game_id": "TEST0002",
                "game_type": "turbo-4",
                "stake_amount": 200,
                "numbers": [1, 2, 3, 4],
                "position": "first"
            }
        ],
        "pool_value": 60000  # Low pool value that might cause the issue
    }
    
    print("Testing Pool Constraint Issue")
    print("=" * 50)
    print(f"Pool value: {data['pool_value']:,}")
    print("Expected: If numbers show 2 winners, both should be in winners list")
    print()
    
    try:
        response = requests.post("http://localhost:5001/nap2_turbo", json=data)
        
        if response.status_code == 200:
            result = response.json()
            
            print("Server Response:")
            print(f"First numbers: {result['first_numbers']}")
            print(f"Last numbers: {result['last_numbers']}")
            print(f"Total winnings: {result['total_winnings']:,}")
            print(f"Winners reported: {len(result['winners'])}")
            print()
            
            # Manually verify what the numbers should produce
            print("Manual Verification:")
            first_numbers = result['first_numbers']
            
            for ticket in data['tickets']:
                game_id = ticket['game_id']
                numbers = ticket['numbers']
                stake = ticket['stake_amount']
                n = len(numbers)
                
                # Check first n positions
                relevant_positions = first_numbers[:n]
                player_set = set(numbers)
                drawn_set = set(relevant_positions)
                matching = player_set.intersection(drawn_set)
                
                print(f"  {game_id}: {numbers}")
                print(f"    Checking first {n} positions: {relevant_positions}")
                print(f"    Matching numbers: {matching} (count: {len(matching)})")
                
                if len(matching) >= 2:
                    # Calculate expected winnings
                    if numbers == relevant_positions:
                        multiplier = {2: 3600, 3: 1200, 4: 600, 5: 360}[n]
                        expected_winnings = stake * multiplier
                        print(f"    ✅ Should WIN (correct order): {expected_winnings:,}")
                    else:
                        multiplier = {2: 1200, 3: 400, 4: 200, 5: 120}[n]
                        expected_winnings = stake * multiplier
                        print(f"    ✅ Should WIN (incorrect order): {expected_winnings:,}")
                else:
                    print(f"    ❌ Should NOT win")
                print()
            
            # Check if there's a mismatch
            winners_in_response = len(result['winners'])
            
            # Count how many should actually win based on the numbers
            should_win_count = 0
            total_expected_winnings = 0
            
            for ticket in data['tickets']:
                numbers = ticket['numbers']
                stake = ticket['stake_amount']
                n = len(numbers)
                relevant_positions = first_numbers[:n]
                player_set = set(numbers)
                drawn_set = set(relevant_positions)
                matching = player_set.intersection(drawn_set)
                
                if len(matching) >= 2:
                    should_win_count += 1
                    if numbers == relevant_positions:
                        multiplier = {2: 3600, 3: 1200, 4: 600, 5: 360}[n]
                    else:
                        multiplier = {2: 1200, 3: 400, 4: 200, 5: 120}[n]
                    total_expected_winnings += stake * multiplier
            
            print("ANALYSIS:")
            print(f"Numbers indicate {should_win_count} winners should win")
            print(f"Server reported {winners_in_response} winners")
            print(f"Expected total winnings: {total_expected_winnings:,}")
            print(f"Server reported winnings: {result['total_winnings']:,}")
            
            if should_win_count != winners_in_response:
                print("🚨 CRITICAL ISSUE: Mismatch between numbers and reported winners!")
                print("This could cause trust issues with players!")
                return False
            elif total_expected_winnings != result['total_winnings']:
                print("🚨 CRITICAL ISSUE: Mismatch between expected and reported winnings!")
                return False
            else:
                print("✅ Numbers and winners match correctly")
                return True
                
        else:
            print(f"❌ HTTP Error {response.status_code}: {response.json()}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    test_pool_constraint_issue()
