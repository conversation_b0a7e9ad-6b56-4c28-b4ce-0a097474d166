#!/usr/bin/env python3
"""
Security and data validation tests for lottery draw algorithm
"""

import unittest
import requests
import json
import sys
import os

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

class TestInputValidation(unittest.TestCase):
    """Test input validation and security"""
    
    BASE_URL = "http://localhost:5001"
    
    def test_malicious_input_injection(self):
        """Test protection against malicious input"""
        malicious_inputs = [
            # SQL injection attempts
            {"tickets": [{"game_id": "'; DROP TABLE tickets; --"}]},
            
            # Script injection attempts  
            {"tickets": [{"game_id": "<script>alert('xss')</script>"}]},
            
            # Path traversal attempts
            {"tickets": [{"game_id": "../../../etc/passwd"}]},
            
            # Large payload attack
            {"tickets": [{"game_id": "A" * 10000}]},
            
            # Invalid JSON structure
            {"tickets": "not_a_list"},
        ]
        
        for malicious_data in malicious_inputs:
            response = requests.post(f"{self.BASE_URL}/simulate", json=malicious_data, timeout=10)
            # Should return 400 Bad Request, not crash
            self.assertEqual(response.status_code, 400)

    def test_boundary_value_testing(self):
        """Test boundary values for all inputs"""
        # Test number boundaries
        boundary_tests = [
            # Numbers out of range
            {"tickets": [{"numbers": [0, 1, 2], "game_type": "turbo-3", "stake_amount": 100, "game_id": "TEST0001"}]},
            {"tickets": [{"numbers": [91, 92, 93], "game_type": "turbo-3", "stake_amount": 100, "game_id": "TEST0001"}]},
            
            # Invalid number counts
            {"tickets": [{"numbers": [1], "game_type": "turbo-1", "stake_amount": 100, "game_id": "TEST0001"}]},  # Too few
            {"tickets": [{"numbers": [1,2,3,4,5,6], "game_type": "turbo-6", "stake_amount": 100, "game_id": "TEST0001"}]},  # Too many
            
            # Invalid stake amounts
            {"tickets": [{"numbers": [1,2,3], "game_type": "turbo-3", "stake_amount": 0, "game_id": "TEST0001"}]},
            {"tickets": [{"numbers": [1,2,3], "game_type": "turbo-3", "stake_amount": -100, "game_id": "TEST0001"}]},
        ]
        
        for test_data in boundary_tests:
            response = requests.post(f"{self.BASE_URL}/nap2_turbo", json={"tickets": test_data["tickets"], "pool_value": 10000}, timeout=10)
            self.assertEqual(response.status_code, 400, f"Should reject invalid input: {test_data}")

    def test_data_type_validation(self):
        """Test data type validation"""
        invalid_type_tests = [
            # Wrong data types
            {"tickets": [{"numbers": "1,2,3", "game_type": "turbo-3", "stake_amount": 100, "game_id": "TEST0001"}]},  # String instead of list
            {"tickets": [{"numbers": [1,2,3], "game_type": 3, "stake_amount": 100, "game_id": "TEST0001"}]},  # Number instead of string
            {"tickets": [{"numbers": [1,2,3], "game_type": "turbo-3", "stake_amount": "100", "game_id": "TEST0001"}]},  # String instead of number
            {"tickets": [{"numbers": [1,2,3], "game_type": "turbo-3", "stake_amount": 100, "game_id": 123}]},  # Number instead of string
        ]
        
        for test_data in invalid_type_tests:
            response = requests.post(f"{self.BASE_URL}/nap2_turbo", json={"tickets": test_data["tickets"], "pool_value": 10000}, timeout=10)
            self.assertEqual(response.status_code, 400, f"Should reject wrong data type: {test_data}")

    def test_required_field_validation(self):
        """Test that all required fields are validated"""
        required_field_tests = [
            # Missing numbers
            {"tickets": [{"game_type": "turbo-3", "stake_amount": 100, "game_id": "TEST0001"}]},
            
            # Missing game_type
            {"tickets": [{"numbers": [1,2,3], "stake_amount": 100, "game_id": "TEST0001"}]},
            
            # Missing stake_amount
            {"tickets": [{"numbers": [1,2,3], "game_type": "turbo-3", "game_id": "TEST0001"}]},
            
            # Missing game_id
            {"tickets": [{"numbers": [1,2,3], "game_type": "turbo-3", "stake_amount": 100}]},
        ]
        
        for test_data in required_field_tests:
            response = requests.post(f"{self.BASE_URL}/nap2_turbo", json={"tickets": test_data["tickets"], "pool_value": 10000}, timeout=10)
            self.assertEqual(response.status_code, 400, f"Should reject missing required field: {test_data}")

class TestBusinessRuleValidation(unittest.TestCase):
    """Test business rule validation"""
    
    BASE_URL = "http://localhost:5001"
    
    def test_game_type_consistency(self):
        """Test that game_type matches number count"""
        inconsistent_tests = [
            # turbo-2 with 3 numbers
            {"game_id": "TEST0001", "game_type": "turbo-2", "numbers": [1,2,3], "stake_amount": 100, "position": "first"},
            
            # turbo-5 with 3 numbers  
            {"game_id": "TEST0002", "game_type": "turbo-5", "numbers": [1,2,3], "stake_amount": 100, "position": "first"},
            
            # turbo-3 with 4 numbers
            {"game_id": "TEST0003", "game_type": "turbo-3", "numbers": [1,2,3,4], "stake_amount": 100, "position": "first"},
        ]
        
        for ticket in inconsistent_tests:
            response = requests.post(f"{self.BASE_URL}/nap2_turbo", 
                                   json={"tickets": [ticket], "pool_value": 10000}, timeout=10)
            self.assertEqual(response.status_code, 400, f"Should reject inconsistent game_type: {ticket}")

    def test_duplicate_numbers_validation(self):
        """Test that duplicate numbers in tickets are handled"""
        duplicate_tests = [
            {"game_id": "TEST0001", "game_type": "turbo-3", "numbers": [1,1,2], "stake_amount": 100, "position": "first"},
            {"game_id": "TEST0002", "game_type": "turbo-4", "numbers": [1,2,2,3], "stake_amount": 100, "position": "first"},
        ]
        
        for ticket in duplicate_tests:
            response = requests.post(f"{self.BASE_URL}/nap2_turbo", 
                                   json={"tickets": [ticket], "pool_value": 10000}, timeout=10)
            # Should either reject or handle gracefully
            self.assertIn(response.status_code, [200, 400])

    def test_position_validation(self):
        """Test position field validation"""
        position_tests = [
            # Invalid position values
            {"game_id": "TEST0001", "game_type": "turbo-3", "numbers": [1,2,3], "stake_amount": 100, "position": "middle"},
            {"game_id": "TEST0002", "game_type": "turbo-3", "numbers": [1,2,3], "stake_amount": 100, "position": ""},
            {"game_id": "TEST0003", "game_type": "turbo-3", "numbers": [1,2,3], "stake_amount": 100, "position": 123},
        ]
        
        for ticket in position_tests:
            response = requests.post(f"{self.BASE_URL}/nap2_turbo", 
                                   json={"tickets": [ticket], "pool_value": 10000}, timeout=10)
            # Should handle invalid positions gracefully (default to 'first')
            self.assertIn(response.status_code, [200, 400])

class TestResourceLimits(unittest.TestCase):
    """Test resource limits and DoS protection"""
    
    BASE_URL = "http://localhost:5001"
    
    def test_large_payload_handling(self):
        """Test handling of very large payloads"""
        # Create a large number of tickets
        large_ticket_set = []
        for i in range(1000):  # 1000 tickets
            large_ticket_set.append({
                "game_id": f"T{i:07d}",
                "game_type": "turbo-3",
                "numbers": [i%10+1, i%10+2, i%10+3],
                "stake_amount": 100,
                "position": "first"
            })
        
        response = requests.post(f"{self.BASE_URL}/nap2_turbo", 
                               json={"tickets": large_ticket_set, "pool_value": 1000000}, 
                               timeout=300)  # 5 minute timeout
        
        # Should either process successfully or reject gracefully
        self.assertIn(response.status_code, [200, 400, 413, 429])

    def test_concurrent_request_limits(self):
        """Test that server handles concurrent requests appropriately"""
        import threading
        import time
        
        results = []
        
        def make_request():
            try:
                ticket = {
                    "game_id": "CONC0001",
                    "game_type": "turbo-2", 
                    "numbers": [1,2],
                    "stake_amount": 100,
                    "position": "first"
                }
                response = requests.post(f"{self.BASE_URL}/nap2_turbo", 
                                       json={"tickets": [ticket], "pool_value": 10000}, 
                                       timeout=30)
                results.append(response.status_code)
            except Exception as e:
                results.append(0)  # Error
        
        # Launch 10 concurrent requests
        threads = []
        for _ in range(10):
            thread = threading.Thread(target=make_request)
            threads.append(thread)
            thread.start()
        
        # Wait for all to complete
        for thread in threads:
            thread.join()
        
        # Most should succeed, some might be rate limited
        success_count = sum(1 for r in results if r == 200)
        self.assertGreater(success_count, 5, "At least half of concurrent requests should succeed")

class TestDataIntegrity(unittest.TestCase):
    """Test data integrity and consistency"""
    
    BASE_URL = "http://localhost:5001"
    
    def test_deterministic_results(self):
        """Test that same input produces same output (when not using random)"""
        # Note: This test may need adjustment if randomization is used
        ticket_data = {
            "tickets": [{
                "game_id": "DETERM01",
                "game_type": "turbo-3",
                "numbers": [1,2,3],
                "stake_amount": 100,
                "position": "first"
            }],
            "pool_value": 50000
        }
        
        # Make the same request twice
        response1 = requests.post(f"{self.BASE_URL}/nap2_turbo", json=ticket_data, timeout=30)
        response2 = requests.post(f"{self.BASE_URL}/nap2_turbo", json=ticket_data, timeout=30)
        
        if response1.status_code == 200 and response2.status_code == 200:
            result1 = response1.json()
            result2 = response2.json()
            
            # Results might be different due to randomization, but structure should be same
            self.assertEqual(set(result1.keys()), set(result2.keys()))

    def test_mathematical_consistency(self):
        """Test that mathematical calculations are consistent"""
        # Create a scenario with known expected results
        ticket = {
            "game_id": "MATH0001",
            "game_type": "turbo-2",
            "numbers": [1,2],
            "stake_amount": 1000,  # Easy to calculate
            "position": "first"
        }
        
        response = requests.post(f"{self.BASE_URL}/nap2_turbo", 
                               json={"tickets": [ticket], "pool_value": 1000000}, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            
            # Verify that total_winnings matches sum of individual winner winnings
            if result['winners']:
                calculated_total = sum(winner['winnings'] for winner in result['winners'])
                self.assertEqual(calculated_total, result['total_winnings'],
                               "Total winnings should equal sum of individual winnings")

if __name__ == '__main__':
    print("Starting Security and Validation Tests...")
    print("Make sure the server is running on localhost:5001")
    print("=" * 60)
    
    unittest.main(verbosity=2)
